import { ref } from 'vue'
import { toast } from './toast'

/**
 * 图片选择钩子函数使用示例
 * @example
 * const { loading, error, selectedImage, run } = useImagePicker({
 *   maxSize: 15, // 最大15MB
 *   sourceType: ['album'], // 仅支持从相册选择
 *   onSuccess: (image) => console.log('选择成功', image),
 *   onError: (err) => console.error('选择失败', err),
 * })
 */

/**
 * 图片选择器选项接口
 */
export interface ImagePickerOptions {
  /** 最大可选择的图片数量，默认为1 */
  count?: number
  /** 所选的图片的尺寸，original-原图，compressed-压缩图 */
  sizeType?: Array<'original' | 'compressed'>
  /** 选择图片的来源，album-相册，camera-相机 */
  sourceType?: Array<'album' | 'camera'>
  /** 文件大小限制，单位：MB */
  maxSize?: number
  /** 选择成功回调函数 */
  onSuccess?: (image: SelectedImageInfo) => void
  /** 选择失败回调函数 */
  onError?: (err: Error | UniApp.GeneralCallbackResult) => void
}

/**
 * 选择的图片信息接口
 */
export interface SelectedImageInfo {
  /** 图片临时文件路径 */
  tempFilePath: string
  /** 图片文件大小（字节） */
  size: number
  /** 图片类型（仅在某些平台可用） */
  type?: string
  /** 图片名称（仅在某些平台可用） */
  name?: string
}

/**
 * 图片选择钩子函数
 * @param options 选择选项
 * @returns 选择状态和控制对象
 */
export function useImagePicker(options: ImagePickerOptions = {}) {
  /** 选择中状态 */
  const loading = ref(false)
  /** 选择错误状态 */
  const error = ref(false)
  /** 选择的图片信息 */
  const selectedImage = ref<SelectedImageInfo>()

  /** 解构选择选项，设置默认值 */
  const {
    /** 最大可选择的图片数量 */
    count = 1,
    /** 所选的图片的尺寸 */
    sizeType = ['original', 'compressed'],
    /** 选择图片的来源 */
    sourceType = ['album', 'camera'],
    /** 文件大小限制（MB） */
    maxSize = 10,
    /** 成功回调 */
    onSuccess,
    /** 失败回调 */
    onError,
  } = options

  /**
   * 检查文件大小是否超过限制
   * @param size 文件大小（字节）
   * @returns 是否通过检查
   */
  const checkFileSize = (size: number) => {
    const sizeInMB = size / 1024 / 1024
    if (sizeInMB > maxSize) {
      toast.warning(`文件大小不能超过${maxSize}MB`)
      return false
    }
    return true
  }

  /**
   * 触发图片选择
   * 根据平台使用不同的选择器：
   * - 微信小程序使用 chooseMedia
   * - 其他平台使用 chooseImage
   */
  const run = () => {
    loading.value = true
    error.value = false
    selectedImage.value = undefined

    // #ifdef MP-WEIXIN
    // 微信小程序环境下使用 chooseMedia API
    uni.chooseMedia({
      count,
      mediaType: ['image'], // 仅支持图片类型
      sourceType,
      success: (res) => {
        const file = res.tempFiles[0]
        // 检查文件大小是否符合限制
        if (!checkFileSize(file.size)) {
          loading.value = false
          return
        }

        // 构建图片信息对象
        const imageInfo: SelectedImageInfo = {
          tempFilePath: file.tempFilePath,
          size: file.size,
          type: file.fileType,
        }

        selectedImage.value = imageInfo
        loading.value = false
        onSuccess?.(imageInfo)
      },
      fail: (err) => {
        console.error('选择媒体文件失败:', err)
        error.value = true
        loading.value = false
        onError?.(err)
      },
    })
    // #endif

    // #ifndef MP-WEIXIN
    // 非微信小程序环境下使用 chooseImage API
    uni.chooseImage({
      count,
      sizeType,
      sourceType,
      success: (res) => {
        console.log('选择图片成功:', res)

        // 获取第一个文件的信息
        const tempFilePath = res.tempFilePaths[0]
        const file = res.tempFiles[0]

        // 检查文件大小是否符合限制
        if (!checkFileSize(file.size)) {
          loading.value = false
          return
        }

        // 构建图片信息对象
        const imageInfo: SelectedImageInfo = {
          tempFilePath,
          size: file.size,
          type: (file as any).type,
          name: (file as any).name,
        }

        selectedImage.value = imageInfo
        loading.value = false
        onSuccess?.(imageInfo)
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        error.value = true
        loading.value = false
        onError?.(err)
      },
    })
    // #endif
  }

  return { loading, error, selectedImage, run }
}
